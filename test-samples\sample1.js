// Sample JavaScript code for testing string compression
// This file contains various types of strings that should be compressed

const greeting = "Hello, World!";
const message = "This is a test message";
const longString = "This is a much longer string that should definitely be compressed because it contains many characters";

// Object with string properties
const config = {
    "apiUrl": "https://api.example.com",
    "timeout": 5000,
    "retries": 3,
    "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer token123"
    }
};

// Function with string literals
function displayMessages() {
    console.log("Starting application...");
    console.log("Loading configuration...");
    console.log("Connecting to API...");
    
    if (config.timeout > 3000) {
        console.log("Using extended timeout");
    }
    
    return "Application started successfully";
}

// Array of strings
const errorMessages = [
    "Invalid input provided",
    "Network connection failed", 
    "Authentication required",
    "Permission denied",
    "Resource not found"
];

// Template literals (should be handled)
const templateMessage = `Welcome ${greeting}! Your session will expire in 30 minutes.`;

// Computed property access
const propertyName = "apiUrl";
const url = config[propertyName];
const contentType = config["headers"]["Content-Type"];

// Switch statement with string cases
function handleError(errorType) {
    switch (errorType) {
        case "network":
            return "Please check your internet connection";
        case "auth":
            return "Please log in again";
        case "permission":
            return "You don't have permission to access this resource";
        default:
            return "An unknown error occurred";
    }
}

// Export for testing
if (typeof module !== 'undefined') {
    module.exports = {
        greeting,
        message,
        config,
        displayMessages,
        errorMessages,
        handleError
    };
}
