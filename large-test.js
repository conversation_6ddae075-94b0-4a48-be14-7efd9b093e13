/**
 * Test string compression with larger code sample
 */

const { processStringCompression } = require('./src/stringCompression');
const fs = require('fs');

async function testLargeFile() {
    console.log('🧪 Testing String Compression with larger file...\n');
    
    try {
        // Read the sample file
        const originalCode = fs.readFileSync('./test-samples/sample1.js', 'utf8');
        
        console.log('Original code length:', originalCode.length, 'characters');
        console.log('\nProcessing...');
        
        const result = await processStringCompression(originalCode);
        
        console.log('\n📊 Results:');
        console.log('Success:', result.success);
        console.log('Strings compressed:', result.stringsCompressed);
        console.log('Unique strings:', result.metadata?.uniqueStrings || 'N/A');
        console.log('Processing time:', result.processingTime + 'ms');
        console.log('Original size:', result.originalSize, 'bytes');
        console.log('Compressed size:', result.compressedSize, 'bytes');
        console.log('Compression ratio:', result.compressionRatio.toFixed(2));
        
        if (result.success) {
            console.log('\n✅ String compression working!');
            
            // Test execution of compressed code
            console.log('\n🔍 Testing execution...');
            try {
                // Create a safe execution environment
                const testCode = result.code + '\n\nconsole.log("Execution test successful");';
                eval(testCode);
                console.log('✅ Compressed code executes successfully');
            } catch (execError) {
                console.log('❌ Execution failed:', execError.message);
            }
            
        } else {
            console.log('\n❌ String compression failed:', result.error);
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        console.error(error.stack);
    }
}

testLargeFile();
