/**
 * Test string compression with object properties and computed access
 */

const { processStringCompression } = require('./src/stringCompression');

async function testObjectProperties() {
    console.log('🧪 Testing String Compression with object properties...\n');
    
    const originalCode = `
        const config = {
            "apiUrl": "https://api.example.com",
            "timeout": 5000,
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer token123"
            }
        };
        
        // Computed property access
        const key = "apiUrl";
        const url = config[key];
        const contentType = config["headers"]["Content-Type"];
        
        // Direct property access
        console.log(config.timeout);
        console.log("Configuration loaded");
    `;
    
    try {
        console.log('Original code:');
        console.log(originalCode);
        console.log('\nProcessing...');
        
        const result = await processStringCompression(originalCode);
        
        console.log('\n📊 Results:');
        console.log('Success:', result.success);
        console.log('Strings compressed:', result.stringsCompressed);
        console.log('Unique strings:', result.metadata?.uniqueStrings || 'N/A');
        console.log('Processing time:', result.processingTime + 'ms');
        
        if (result.success) {
            console.log('\n✅ String compression working!');
            
            console.log('\n🔍 Compressed code preview (first 1000 chars):');
            console.log(result.code.substring(0, 1000) + '...');
            
            // Test that the code structure is preserved
            console.log('\n🔍 Checking code structure:');
            console.log('- Contains computed property syntax [...]:', result.code.includes('['));
            console.log('- Contains object literal syntax {...}:', result.code.includes('{'));
            console.log('- Contains function calls:', result.code.includes('('));
            
        } else {
            console.log('\n❌ String compression failed:', result.error);
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        console.error(error.stack);
    }
}

testObjectProperties();
