/**
 * Test string compression with require/import statements
 */

const { processStringCompression } = require('./src/stringCompression');

async function testRequireImport() {
    console.log('🧪 Testing String Compression with require/import statements...\n');
    
    const originalCode = `
        const fs = require("fs");
        const path = require("path");
        import { Component } from "react";
        
        const message = "This should be compressed";
        const config = {
            "apiUrl": "https://api.example.com",
            "timeout": 5000
        };
        
        console.log("Application started");
        console.log(message);
    `;
    
    try {
        console.log('Original code:');
        console.log(originalCode);
        console.log('\nProcessing...');
        
        const result = await processStringCompression(originalCode);
        
        console.log('\n📊 Results:');
        console.log('Success:', result.success);
        console.log('Strings compressed:', result.stringsCompressed);
        console.log('Processing time:', result.processingTime + 'ms');
        
        if (result.success) {
            console.log('\n✅ String compression working!');
            
            // Check if require/import strings are preserved
            const hasFs = result.code.includes('"fs"') || result.code.includes("'fs'");
            const hasPath = result.code.includes('"path"') || result.code.includes("'path'");
            const hasReact = result.code.includes('"react"') || result.code.includes("'react'");
            
            console.log('\n🔍 Checking require/import preservation:');
            console.log('- "fs" preserved:', hasFs);
            console.log('- "path" preserved:', hasPath);
            console.log('- "react" preserved:', hasReact);
            
            // Check if other strings were compressed
            const hasMessage = result.code.includes('"This should be compressed"');
            const hasApiUrl = result.code.includes('"https://api.example.com"');
            const hasAppStarted = result.code.includes('"Application started"');
            
            console.log('\n🔍 Checking string compression:');
            console.log('- Message compressed (should be true):', !hasMessage);
            console.log('- API URL compressed (should be true):', !hasApiUrl);
            console.log('- App started compressed (should be true):', !hasAppStarted);
            
            if ((hasFs || hasPath || hasReact) && (!hasMessage && !hasApiUrl && !hasAppStarted)) {
                console.log('\n🎉 Perfect! Require/import strings preserved, other strings compressed!');
            } else {
                console.log('\n⚠️  Mixed results - check implementation');
            }
            
        } else {
            console.log('\n❌ String compression failed:', result.error);
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        console.error(error.stack);
    }
}

testRequireImport();
