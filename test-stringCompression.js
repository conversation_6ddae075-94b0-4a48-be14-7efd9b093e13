/**
 * String Compression Test Suite
 * 
 * This file tests the custom string compression functionality
 * to ensure it works correctly with various types of JavaScript code.
 */

const { processStringCompression } = require('./src/stringCompression');
const { executeCodeSafely } = require('./src/utils');

// Test counter
let testCount = 0;
let passedTests = 0;
let failedTests = 0;

/**
 * Helper function to run a test case
 */
async function runTest(testName, testFunction) {
    testCount++;
    console.log(`\n🧪 Test ${testCount}: ${testName}`);
    console.log('=' .repeat(50));

    try {
        const result = await testFunction();
        if (result) {
            console.log('✅ PASSED');
            passedTests++;
        } else {
            console.log('❌ FAILED');
            failedTests++;
        }
    } catch (error) {
        console.log('❌ FAILED with error:', error.message);
        failedTests++;
    }
}

/**
 * Test basic string compression
 */
function testBasicStringCompression() {
    const originalCode = `
        console.log("Hello World");
        console.log("This is a test");
        const message = "Another string";
        alert("Final message");
    `;
    
    return processStringCompression(originalCode).then(result => {
        console.log('Original code length:', originalCode.length);
        console.log('Compressed code length:', result.code.length);
        console.log('Strings compressed:', result.stringsCompressed);
        console.log('Processing time:', result.processingTime + 'ms');
        
        // Test that compressed code executes without errors
        const execution = executeCodeSafely(result.code);
        console.log('Execution successful:', execution.success);
        
        return result.success && result.stringsCompressed > 0 && execution.success;
    });
}

/**
 * Test object properties compression
 */
function testObjectPropertiesCompression() {
    const originalCode = `
        const obj = {
            "property1": "value1",
            "property2": "value2",
            "method": function() {
                return "method result";
            }
        };
        console.log(obj["property1"]);
        console.log(obj.property2);
    `;
    
    return processStringCompression(originalCode).then(result => {
        console.log('Original code length:', originalCode.length);
        console.log('Compressed code length:', result.code.length);
        console.log('Strings compressed:', result.stringsCompressed);
        
        // Test execution
        const execution = executeCodeSafely(result.code);
        console.log('Execution successful:', execution.success);
        
        return result.success && result.stringsCompressed > 0 && execution.success;
    });
}

/**
 * Test computed property access
 */
function testComputedPropertyAccess() {
    const originalCode = `
        const obj = {"key1": "value1", "key2": "value2"};
        const key = "key1";
        console.log(obj[key]);
        console.log(obj["key2"]);
    `;
    
    return processStringCompression(originalCode).then(result => {
        console.log('Original code length:', originalCode.length);
        console.log('Compressed code length:', result.code.length);
        console.log('Strings compressed:', result.stringsCompressed);
        
        // Test execution
        const execution = executeCodeSafely(result.code);
        console.log('Execution successful:', execution.success);
        
        return result.success && execution.success;
    });
}

/**
 * Test that require/import statements are skipped
 */
function testRequireImportSkipping() {
    const originalCode = `
        const fs = require("fs");
        import { something } from "module";
        const message = "This should be compressed";
        console.log(message);
    `;
    
    return processStringCompression(originalCode).then(result => {
        console.log('Original code length:', originalCode.length);
        console.log('Compressed code length:', result.code.length);
        console.log('Strings compressed:', result.stringsCompressed);
        
        // Check that require/import strings are still present
        const hasRequire = result.code.includes('"fs"') || result.code.includes("'fs'");
        const hasImport = result.code.includes('"module"') || result.code.includes("'module'");
        
        console.log('Require statement preserved:', hasRequire);
        console.log('Import statement preserved:', hasImport);
        
        return result.success && (hasRequire || hasImport);
    });
}

/**
 * Test edge cases
 */
function testEdgeCases() {
    const originalCode = `
        const empty = "";
        const single = "a";
        const withDelimiter = "test|with|delimiter";
        const normal = "normal string";
        console.log(normal);
    `;
    
    return processStringCompression(originalCode).then(result => {
        console.log('Original code length:', originalCode.length);
        console.log('Compressed code length:', result.code.length);
        console.log('Strings compressed:', result.stringsCompressed);
        
        // Test execution
        const execution = executeCodeSafely(result.code);
        console.log('Execution successful:', execution.success);
        
        // Should only compress "normal string" (others are too short or contain delimiter)
        return result.success && execution.success;
    });
}

/**
 * Test error handling
 */
function testErrorHandling() {
    const invalidCode = `
        const obj = {
            "key": "value",
            "key2": "value2"
        };
        // This will cause an error during execution, not parsing
        nonExistentFunction();
    `;

    return processStringCompression(invalidCode).then(result => {
        console.log('Processing result success:', result.success);
        console.log('Error message:', result.error || 'No error');

        // Should process successfully but execution might fail
        // The string compression should still work even if the code has runtime errors
        return result.success && result.stringsCompressed >= 0;
    });
}

/**
 * Test compression statistics
 */
function testCompressionStatistics() {
    const originalCode = `
        const messages = [
            "First message",
            "Second message", 
            "Third message",
            "First message", // Duplicate
            "Fourth message"
        ];
        messages.forEach(msg => console.log(msg));
    `;
    
    return processStringCompression(originalCode).then(result => {
        console.log('Compression Statistics:');
        console.log('- Original size:', result.originalSize, 'bytes');
        console.log('- Compressed size:', result.compressedSize, 'bytes');
        console.log('- Compression ratio:', result.compressionRatio.toFixed(2));
        console.log('- Unique strings:', result.metadata.uniqueStrings);
        console.log('- Strings processed:', result.stringsProcessed);
        console.log('- Processing time:', result.processingTime + 'ms');
        
        return result.success && result.metadata.uniqueStrings > 0;
    });
}

/**
 * Run all tests
 */
async function runAllTests() {
    console.log('🚀 Starting String Compression Test Suite');
    console.log('==========================================\n');
    
    // Run all test cases
    await runTest('Basic String Compression', () => testBasicStringCompression());
    await runTest('Object Properties Compression', () => testObjectPropertiesCompression());
    await runTest('Computed Property Access', () => testComputedPropertyAccess());
    await runTest('Require/Import Skipping', () => testRequireImportSkipping());
    await runTest('Edge Cases', () => testEdgeCases());
    await runTest('Error Handling', () => testErrorHandling());
    await runTest('Compression Statistics', () => testCompressionStatistics());
    
    // Print summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total tests: ${testCount}`);
    console.log(`Passed: ${passedTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Success rate: ${((passedTests / testCount) * 100).toFixed(1)}%`);
    
    if (failedTests === 0) {
        console.log('\n🎉 All tests passed! String compression is working correctly.');
    } else {
        console.log('\n⚠️  Some tests failed. Please review the implementation.');
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    runAllTests,
    testBasicStringCompression,
    testObjectPropertiesCompression,
    testComputedPropertyAccess,
    testRequireImportSkipping,
    testEdgeCases,
    testErrorHandling,
    testCompressionStatistics
};
