/**
 * Simple test for string compression
 */

const { processStringCompression } = require('./src/stringCompression');

async function simpleTest() {
    console.log('🧪 Testing String Compression...\n');
    
    const originalCode = `
        console.log("Hello World");
        console.log("This is a test");
        const message = "Another string";
    `;
    
    try {
        console.log('Original code:');
        console.log(originalCode);
        console.log('\nProcessing...');
        
        const result = await processStringCompression(originalCode);
        
        console.log('\n📊 Results:');
        console.log('Success:', result.success);
        console.log('Strings compressed:', result.stringsCompressed);
        console.log('Processing time:', result.processingTime + 'ms');
        console.log('Original size:', result.originalSize, 'bytes');
        console.log('Compressed size:', result.compressedSize, 'bytes');
        console.log('Compression ratio:', result.compressionRatio.toFixed(2));
        
        if (result.success) {
            console.log('\n✅ String compression working!');
            console.log('\nCompressed code preview (first 500 chars):');
            console.log(result.code.substring(0, 500) + '...');
        } else {
            console.log('\n❌ String compression failed:', result.error);
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        console.error(error.stack);
    }
}

simpleTest();
