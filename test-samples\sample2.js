// Sample with require/import statements that should NOT be compressed
const fs = require("fs");
const path = require("path");
const axios = require("axios");
import { Component } from "react";
import { Router } from "express";

// These strings SHOULD be compressed
const appName = "My Application";
const version = "1.0.0";
const description = "This is a sample application for testing string compression";

// Object with mixed content
const packageInfo = {
    "name": appName,
    "version": version,
    "description": description,
    "main": "index.js",
    "scripts": {
        "start": "node index.js",
        "test": "npm run test-all",
        "build": "webpack --mode production"
    },
    "dependencies": {
        "express": "^4.18.0",
        "axios": "^1.0.0"
    }
};

// Function that uses both require strings (should not compress) and regular strings (should compress)
function loadModule(moduleName) {
    try {
        const module = require(moduleName); // This require string should NOT be compressed
        console.log("Successfully loaded module"); // This should be compressed
        return module;
    } catch (error) {
        console.log("Failed to load module"); // This should be compressed
        throw new Error("Module loading failed"); // This should be compressed
    }
}

// Dynamic imports (should not compress the module path)
async function dynamicImport(modulePath) {
    try {
        const module = await import(modulePath); // Should not compress
        console.log("Dynamic import successful"); // Should compress
        return module;
    } catch (error) {
        console.log("Dynamic import failed"); // Should compress
        return null;
    }
}

// Regular strings that should be compressed
const messages = {
    "welcome": "Welcome to our application!",
    "goodbye": "Thank you for using our service!",
    "error": "Something went wrong. Please try again.",
    "success": "Operation completed successfully!"
};

// Computed property access with strings
const messageKey = "welcome";
const welcomeMessage = messages[messageKey];
const errorMessage = messages["error"];

console.log("Application initialized"); // Should be compressed
console.log("Version:", version); // "Version:" should be compressed, version variable reference is fine
