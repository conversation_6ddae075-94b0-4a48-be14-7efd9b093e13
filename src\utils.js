// utils.js
// Consolidated utility functions for the obfuscator

const axios = require('axios');



/**
 * Generates a random variable name for obfuscation
 * @returns {string} - Random variable name
 */
function generateRandomName(length = Math.floor(Math.random() * 6) + 5) {
    const chars = ['_','$']
    const chars2 = ["̖", "̗", "̘", "̙", "̜", "̝", "̞", "̟", "̠", "̤", "̥", "̦", "̩", "̪", "̫", "̬", "̭", "̮", "̯", "̰", "̱", "̲", "̳", "̹", "̺", "̻", "̼", "ͅ", "͇", "͈", "͉", "͍", "͎", "͓", "͔", "͕", "͖", "͙", "͚", "̣","ู","ุ"];
    let result = '';

    for (let i = 0; i < length; i++) {
        result += chars[Math.floor(Math.random() * chars.length)];
        result += chars2[Math.floor(Math.random() * chars2.length)];
    }

    return result;
}



/**
 * Encodes a string using Base64
 * @param {string} str - String to encode
 * @returns {string} - Encoded string
 */
function encodeString(str) {
    // First, encode with Base64
    const base64Encoded = Buffer.from(str, 'utf8').toString('base64');

    return base64Encoded;
}



/**
 * Generates realistic decoy strings to confuse analysis
 * @param {number} count - Number of decoy strings to generate
 * @returns {Array} - Array of decoy strings
 */
function generateDecoyStrings(count = Math.floor(Math.random() * 6) + 5) {
    const decoys = [];

    for (let i = 0; i < count; i++) {
        let decoyString = '';
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        const length = Math.floor(Math.random() * 10) + 5; // 5-15 characters
        for (let j = 0; j < length; j++) {
            decoyString += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        decoys.push(decoyString);
    }

    return decoys;
}

/**
 * Shuffles an array using Fisher-Yates algorithm
 * @param {Array} array - Array to shuffle
 * @returns {Array} - Shuffled array
 */
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

/**
 * Helper function to add timeout to promises
 * @param {Promise} promise - Promise to add timeout to
 * @param {number} timeoutMs - Timeout in milliseconds
 * @param {string} errorMessage - Error message for timeout
 * @returns {Promise} - Promise with timeout
 */
function withTimeout(promise, timeoutMs, errorMessage) {
    let timeoutId;

    const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
            reject(new Error(errorMessage));
        }, timeoutMs);
    });

    // Make sure to clear the timeout if the promise resolves before the timeout
    return Promise.race([
        promise.then(result => {
            clearTimeout(timeoutId);
            return result;
        }),
        timeoutPromise
    ]);
}

/**
 * Helper function to safely execute a function with error handling
 * @param {Function} fn - Function to execute
 * @param {*} fallback - Fallback value if function fails
 * @param {string} errorMessage - Error message prefix
 * @returns {*} - Function result or fallback
 */
function safeExecute(fn, fallback, errorMessage) {
    try {
        return fn();
    } catch (error) {
        console.error(`${errorMessage}: ${error.message}`);
        return fallback;
    }
}

/**
 * Helper function to safely execute an async function with error handling
 * @param {Function} fn - Async function to execute
 * @param {*} fallback - Fallback value if function fails
 * @param {string} errorMessage - Error message prefix
 * @returns {Promise} - Function result or fallback
 */
async function safeExecuteAsync(fn, fallback, errorMessage) {
    try {
        return await fn();
    } catch (error) {
        console.error(`${errorMessage}: ${error.message}`);
        return fallback;
    }
}

/**
 * Verifies a Cloudflare Turnstile token
 * @param {string} token - Turnstile token to verify
 * @returns {Promise<boolean>} - True if token is valid, false otherwise
 */
async function verifyTurnstileToken(token) {
    try {
        const TURNSTILE_SECRET_KEY = '0x4AAAAAAA6jDZFBlAWtrvTcndGWuodhRmQ';
        const response = await axios.post('https://challenges.cloudflare.com/turnstile/v0/siteverify',
            new URLSearchParams({
                secret: TURNSTILE_SECRET_KEY,
                response: token
            }), {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
        );
        return response.data.success;
    } catch (error) {
        console.error('Turnstile verification failed:', error);
        return false;
    }
}



/**
 * Helper function to execute JavaScript code safely and capture output
 * @param {string} code - JavaScript code to execute
 * @returns {Object} - Result object with success status and output/error
 */
function executeCodeSafely(code) {
    try {
        // Create a new context to avoid polluting global scope
        const originalConsoleLog = console.log;
        const outputs = [];

        // Capture console.log output
        console.log = (...args) => {
            outputs.push(args.join(' '));
        };

        // Execute the code
        const result = eval(code);

        // Restore original console.log
        console.log = originalConsoleLog;

        return {
            success: true,
            result: result,
            outputs: outputs,
            error: null
        };
    } catch (error) {
        return {
            success: false,
            result: null,
            outputs: [],
            error: error.message
        };
    }
}

module.exports = {
    // Random generation utilities
    generateRandomName,
    generateDecoyStrings,

    // String encoding/decoding utilities
    encodeString,

    // Array utilities
    shuffleArray,

    // Promise utilities
    withTimeout,
    safeExecute,
    safeExecuteAsync,

    // Testing utilities
    executeCodeSafely,

    // External service utilities
    verifyTurnstileToken
};